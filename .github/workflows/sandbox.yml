name: Sandbox deploy

# This workflow is triggered when changes are pushed to the main branch or manually dispatched.
# It builds and deploys the application to the sandbox environment.
on:
  push:
    branches:
      - main
  workflow_dispatch:
    inputs:
      tag:
        description: 'Release tag to deploy (optional)'
        required: false
        type: string


jobs:
  build:

    runs-on: ubuntu-latest

    steps:
      - name: Check out code
        uses: actions/checkout@v2

      - name: Configure node version
        uses: actions/setup-node@v2
        with:
          node-version: '14.15.1'

      - name: Install env-cmd
        run: |
          yarn global add env-cmd

      - name: Install dependencies
        run: |
          yarn install

      - name: Build static site
        run: |
          env-cmd -f .env.sandbox yarn build
        env:
          CI: false

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Deploy static site to S3 bucket
        run: |
          aws s3 sync build/ s3://totem-sandbox.lavomat.com.uy
