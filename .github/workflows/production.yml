name: Production deploy

# This workflow is triggered only when it is manually dispatched.
# It builds and deploys the application to the production environment.
on:
  workflow_dispatch:
    inputs:
      tag:
        description: 'Release tag to deploy (required)'
        required: true
        type: string

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
      - name: Check out code
        uses: actions/checkout@v2
        with:
          ref: ${{ inputs.tag }}

      - name: Configure node version
        uses: actions/setup-node@v2
        with:
          node-version: '14.15.1'

      - name: Install env-cmd
        run: |
          yarn global add env-cmd

      - name: Install dependencies
        run: |
          yarn install

      - name: Build static site
        run: |
          env-cmd -f .env.production yarn build
        env:
          CI: false

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Deploy static site to S3 bucket
        run: |
          aws s3 sync build/ s3://lavomat-totem-app-production

      - name: Create invalidation
        run: |
          aws cloudfront create-invalidation --distribution-id ${{ secrets.AWS_CLOUDFRONT_PROD_DISTRIBUTION_ID }} --paths '/*'
