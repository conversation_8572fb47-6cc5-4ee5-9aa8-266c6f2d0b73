## Available Scripts

In the project directory, you can run:

### `yarn start`

Runs the app in the development mode.<br>
Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

The page will reload if you make edits.<br>
You will also see any lint errors in the console.


## Release new version

1. Update `package.json` app version (_mandatory_)
1. Follow the [Deployment](#deployment) steps.

## Deployment
To **PROD** use `Production deploy` workflow with the tag to deploy as parameter.

To **SANDBOX** is automatic on every commit to `main`.

## linters
```
yarn install --save-dev stylelint-config-standard stylelint-order stylelint npm-run-all eslint-config-airbnb eslint-config-prettier eslint-plugin-cypress eslint-plugin-import eslint-plugin-jsx-a11y eslint-plugin-react eslint-plugin-react-hooks eslint-plugin-prettier eslint prettier
```

### VS Code extensions
* ESLint by <PERSON>
* Prettier - Code formatter by <PERSON><PERSON><PERSON>
* stylelint by stylelint
