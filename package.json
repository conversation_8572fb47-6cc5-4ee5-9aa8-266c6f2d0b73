{"name": "lavomat-totem", "version": "3.1.2", "private": true, "scripts": {"start": "react-scripts start", "build": "GENERATE_SOURCEMAP=false react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "build:sandbox": "env-cmd -f .env.sandbox npm run build", "deploy:sandbox": "env-cmd -f .env.sandbox npm run build && aws s3 sync build/ s3://totem-sandbox.lavomat.com.uy --profile lavomat", "build:production": "env-cmd -f .env.production npm run build", "deploy:production": "env-cmd -f .env.production npm run build && aws s3 sync build/ s3://lavomat-totem-app-production --profile lavomat", "postdeploy:production": "aws cloudfront create-invalidation --distribution-id E38KRGEH23XYCM --paths '/*' --profile lavomat", "lint:code": "eslint src --color", "lint:code:fix": "eslint --fix src", "lint:style": "stylelint \"src/**/*.{css,scss,sass}\"", "lint:style:fix": "stylelint --fix \"src/**/*.{css,scss,sass}\"", "lint": "npm-run-all lint:code lint:style", "lint:fix": "npm-run-all lint:code:fix lint:style:fix", "lint:code:prettier": "prettier --write './src/**/*.js'"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx,json}": ["prettier --write", "eslint --fix", "git add"], "src/**/*.{css,scss,sass}": ["stylelint --fix", "git add"]}, "dependencies": {"@sentry/react": "^6.1.0", "@sentry/tracing": "^6.1.0", "antd": "^3.15.0", "axios": "^0.18.0", "env-cmd": "^10.1.0", "history": "^4.9.0", "intl": "^1.2.5", "lodash": "^4.17.15", "moment": "^2.24.0", "node-sass": "^4.11.0", "prop-types": "^15.7.2", "react": "^16.9.0", "react-countdown-now": "^2.1.1", "react-dom": "^16.9.0", "react-error-boundary": "^4.0.11", "react-pdf": "^4.1.0", "react-redux": "^6.0.1", "react-router-dom": "^4.3.1", "react-scripts": "3.1.1", "react-touch-screen-keyboard": "^1.0.0", "redux": "^4.0.1", "redux-logger": "^3.0.6", "redux-promise-middleware": "^6.1.0", "sass-loader": "^7.1.0"}, "devDependencies": {"eslint": "^6.1.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-prettier": "^7.2.0", "eslint-plugin-cypress": "^2.11.2", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-react": "^7.22.0", "eslint-plugin-react-hooks": "^4.2.0", "husky": "^4.3.8", "lint-staged": "^10.5.4", "npm-run-all": "^4.1.5", "prettier": "^2.2.1", "stylelint": "^13.9.0", "stylelint-config-standard": "^20.0.0", "stylelint-order": "^4.1.0"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}