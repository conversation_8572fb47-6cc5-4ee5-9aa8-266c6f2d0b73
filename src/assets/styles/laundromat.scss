@import "./variables.scss";

.laundromat {
  .list {
    .header {
      box-shadow: 0 0 8px #000;
      color: #fff;
      display: flex;
      flex-direction: row;
      padding: 10px;
      position: relative;

      &.washer {
        background-color: $washer;

        &::after {
          border-bottom: none;
          border-left: 15px solid transparent;
          border-right: 15px solid transparent;
          border-top: 15px solid $washer;
          bottom: -15px;
          content: " ";
          position: absolute;
          right: 50%;
        }
      }

      &.dryer {
        background-color: $dryer;

        &::after {
          border-bottom: none;
          border-left: 15px solid transparent;
          border-right: 15px solid transparent;
          border-top: 15px solid $dryer;
          bottom: -15px;
          content: " ";
          position: absolute;
          right: 50%;
        }
      }

      div {
        flex: 1;
      }

      img {
        width: 42px;
      }

      .type-box {
        flex: 2;
        font-size: 24px;
        text-align: left;
      }

      .price-box {
        background-color: rgba(0, 0, 0, .6);
        font-size: 20px;
        font-weight: bold;
        padding: 5px;
      }
    }

    .machine {
      background-color: $machine-box;
      display: flex;
      margin: 20px 0 20px 0;
      padding: 10px;

      .number {
        background-color: $primary-color;
        border-radius: 5px;
        display: flex;
        height: 80px;
        justify-content: center;
        width: 60px;

        .index {
          align-self: center;
          background-color: #fff;
          border-radius: 100%;
          color: $primary-color;
          font-size: 34px;
          height: 50px;
          margin: 0;
          width: 50px;
        }

        .informative {
          display: flex;
          justify-content: flex-end;
          position: absolute;
          width: 60px;

          span {
            color: white;
            font-family: Georgia, 'Times New Roman', Times, serif;
            font-size: 18px;
            font-style: italic;
            font-weight: bolder;
            margin-right: 5px;
          }
        }
      }

      &.in-use {
        background-color: $in-use-color;

        .number {
          background-color: #6a1109;

          .index {
            color: #6a1109;
          }
        }
      }

      $border-active: #48766e;
      $border-disabled: #6f0000;

      .info-container {
        $margin-x: 5%;
        $margin-y: 10px;

        &__header {
          color: white;
          font-size: 1.5rem;
          margin: 0 $margin-x;
          padding-bottom: 12px;

          &.active {
            border-bottom: 2px solid $border-active;

            .caret-right-icon {
              color: $border-active;
              font-size: 18px;
              margin: 0 5px;
            }
          }

          &.disabled {
            border-bottom: 2px solid $border-disabled;

            .timer {
              .clock-circle-icon {
                margin: 0 .5rem;
                text-align: right;
              }

              span {
                margin-left: 10px;
                text-align: left;
              }
            }
          }
        }

        &__action {
          font-size: 1.7rem;
          line-height: 30px;
          margin: $margin-y $margin-x 0 $margin-x;
          text-align: center;
          text-transform: uppercase;

          .active {
            color: #fbf384;
          }

          .disabled {
            color: #da0101;
          }
        }
      }
    }
  }
}
