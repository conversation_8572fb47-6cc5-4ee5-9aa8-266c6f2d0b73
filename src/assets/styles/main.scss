@import './variables.scss';
@import './utils.scss';
@import 'react-touch-screen-keyboard/lib/Keyboard.scss';
@import './fonts.scss';

body {
  background-color: $secondary-color !important;

  // Disable Text Selection
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none; /* Standard syntax */
}

#root {
  height: 100%;
}

.main {
  text-align: center;

  .pos {
    margin-top: 100px;
    width: 29%;
  }

  h1 {
    color: #fff;
    font-size: 30px;
    margin-bottom: 50px;
  }

  h2 {
    color: #fff;
    font-size: 25px;
  }

  .mainHeader {
    background-color: $primary-color;
    height: 100px;
    padding: 30px 0;

    img {
      width: 200px;
    }
  }

  .success {
    color: $primary-color;
    font-size: 132px;

    &.small {
      font-size: 50px;
    }
  }

  .contentMain {
    background-color: $secondary-color;
    padding-top: 20px;
  }

  .information-container {
    background-color: $dark;
    min-height: 200px;
    width: 100%;
  }

  .ant-btn {
    background-color: $dark;
    border: none;
    color: $action-color;
    font-size: 20px;
    font-weight: bold;
    height: 50px;
    padding: 10px 20px;
    text-transform: uppercase;

    &.ant-btn-link {
      background-color: transparent;
      color: $action-color;
      margin-bottom: 20px;
      text-transform: capitalize;
    }

    &:hover {
      background-color: $dark;
      color: $action-color;
    }

    &:focus {
      background-color: $dark;
      color: $action-color;
    }

    &.transparent {
      background-color: transparent !important;
      color: #fff !important;
    }
  }

  .ant-col {
    margin-bottom: 50px;
  }

  .keyboard {
    background-color: #fff;
    flex: .5;
    margin-bottom: 20px;
    padding: 5px;

    &.small {
      height: 40px;
      margin-left: -121%;
      width: 130px;
    }
  }

  .info {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: center;

    div {
      margin-left: 20px;
      margin-right: 20px;
    }
  }
}
