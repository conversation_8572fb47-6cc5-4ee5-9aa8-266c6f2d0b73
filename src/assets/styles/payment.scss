@import "./variables.scss";

.payment {
  .payment-method {
    height: 200px;
    width: 450px;

    img {
      margin: 20px;
    }

    &.cards {
      height: 300px;
    }
  }

  .breakdown {
    .row {
      border-top: 1px solid $primary-color;
      color: $primary-color;
      display: flex;
      flex-direction: row;
      height: 70px;
      padding: 10px;

      &.highlighted {
        color: #fff;
      }

      div {
        flex: 1;
        font-size: 32px;
        text-align: left;
      }

      .amount {
        flex: .5;
        font-weight: bold;
      }

      &.total {
        background-color: $primary-color;
        color: #fff;
        font-weight: bold;
      }
    }
  }
}

.receipt {
  .information-container {
    align-items: center;
    height: 500px;
    justify-content: center;
    overflow-y: scroll;
  }
}
