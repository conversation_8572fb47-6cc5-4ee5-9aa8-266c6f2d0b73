import isClosureSoon from './isClosureSoon'

describe('isClosureSoon', () => {
  beforeEach(() => {
    jest
      .spyOn(global.Date, 'now')
      .mockImplementation(() => new Date('2025-01-01T20:00:00').getTime())
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  it('returns false when no time is provided', () => {
    const { isSoon, closureTime } = isClosureSoon(null)

    expect(isSoon).toBeFalsy()
    expect(closureTime).toBeUndefined()
  })

  it('returns isSoon true when closing time is less than 90 minutes away', () => {
    const closingTime = new Date('2025-01-01T21:00:00').getTime()

    const { isSoon, closureTime } = isClosureSoon(closingTime)

    expect(isSoon).toBeTruthy()
    expect(closureTime).toEqual('21:00')
  })

  it('returns isSoon false when closing time is more than 90 minutes away', () => {
    const closingTime = new Date('2025-01-01T22:00:00').getTime()

    const { isSoon, closureTime } = isClosureSoon(closingTime)

    expect(isSoon).toBeFalsy()
    expect(closureTime).toEqual('22:00')
  })

  it('returns isSoon true when closing time is exactly 90 minutes away', () => {
    const closingTime = new Date('2025-01-01T21:30:00').getTime()

    const { isSoon, closureTime } = isClosureSoon(closingTime)

    expect(isSoon).toBeTruthy()
    expect(closureTime).toEqual('21:30')
  })

  it('returns isSoon true even if the closing date is in the past', () => {
    const closingTime = new Date('2024-01-01T21:00:00').getTime()

    const { isSoon, closureTime } = isClosureSoon(closingTime)

    expect(isSoon).toBeTruthy()
    expect(closureTime).toEqual('21:00')
  })

  it('returns isSoon true even if the closing date is in the future', () => {
    const closingTime = new Date('2026-01-01T21:00:00').getTime()

    const { isSoon, closureTime } = isClosureSoon(closingTime)

    expect(isSoon).toBeTruthy()
    expect(closureTime).toEqual('21:00')
  })

  it('returns isSoon true even if the closing time is in the past too much', () => {
    const closingTime = new Date('1970-01-01T21:00:00.000').getTime()

    const { isSoon, closureTime } = isClosureSoon(closingTime)

    expect(isSoon).toBeTruthy()
    expect(closureTime).toEqual('21:00')
  })

  describe.each`
    hours    | minutes
    ${'03'}  | ${'00'}
    ${'06'}  | ${'05'}
    ${'09'}  | ${'10'}
    ${'12'}  | ${'15'}
    ${'15'}  | ${'20'}
    ${'18'}  | ${'25'}
    ${'21'}  | ${'30'}
  `('closure time formatting', ({ hours, minutes }) => {
    beforeEach(() => {
      jest
        .spyOn(global.Date, 'now')
        .mockImplementation(() => new Date(`2025-02-01T${hours}:${minutes}:00`).getTime())
    })

    it(`returns always double digits for hours and minutes: ${hours}:${minutes}`, () => {
      const closingTime = new Date(`2025-02-01T${hours}:${minutes}:00`).getTime()

      const { closureTime } = isClosureSoon(closingTime)
      expect(closureTime).toEqual(`${hours}:${minutes}`)
    })
  })
})
