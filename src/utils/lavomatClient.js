import axios from 'axios'
import { BASE_URL } from '../constants'

export const instance = axios.create({
  baseURL: BASE_URL,
  timeout: 20000,
})

export const buildRequest = (url, token, params, method) => ({
  url,
  params: method === 'GET' && params,
  data: method === 'POST' && params,
  method,
  headers: {
    Accept: '*',
    'Content-Type': 'application/json',
    'X-LM-AUTH-TOKEN': token,
  },
})

export const get = (url, params, token) =>
  instance.request(buildRequest(url, token, params, 'GET'))

export const post = (url, params, token) =>
  instance.request(buildRequest(url, token, params, 'POST'))

export const postForPDF = (url, params, token) =>
  instance.request({
    ...buildRequest(url, token, params, 'POST'),
    responseType: 'arraybuffer',
  })

export const deleteAction = (url, params, token) =>
  instance.request(buildRequest(url, token, params, 'DELETE'))
