import { captureException, captureEvent, Severity, init } from '@sentry/react'
import { Integrations } from '@sentry/tracing'

import { SENTRY_ENVIRONMENT, SENTRY_CONFIG } from '../constants'

const isDevelopEnv = () => SENTRY_ENVIRONMENT === 'develop'

export const initialize = () => {
  if (isDevelopEnv()) return

  const options = {
    integrations: [new Integrations.BrowserTracing()],
    ...SENTRY_CONFIG,
  }

  init(options)
}

export const trackAction = action => {
  if (isDevelopEnv()) {
    console.debug('Reducer action', action)
    return // avoid sending developing issues
  }

  if (!action.type.endsWith('_REJECTED')) return

  try {
    const { type, payload } = action
    const { response, config } = payload

    let eventMessage; let tags
    if (response) {
      const { status } = response
      const { result_code, result_detail, result_message } = response.data

      eventMessage = `Request failed with status ${status}`
      tags = {
        'result.code': result_code,
        'result.detail': result_detail,
        'result.message': result_message,
      }
    } else {
      const { message, stack } = payload

      eventMessage = `Internal Error: ${message}`
      tags = { 'result.stack': stack }
    }

    captureEvent({
      level: Severity.Fatal,
      request: config,
      message: eventMessage,
      transaction: type,
      tags,
    })
  } catch (err) {
    captureException(err)
  }
}

export default {
  trackAction,
  initialize,
}
