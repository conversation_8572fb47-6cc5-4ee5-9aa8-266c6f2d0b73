import { ALERT_CLOSURE_SOON_TIME } from '../constants'

const doubleDigits = value => value < 10 ? `0${value}` : value

const isClosureSoon = time => {
  if (!time) return { isSoon: false }

  const now = new Date(Date.now())
  now.setSeconds(0)

  const timeDate = new Date(time)
  const closingTime = new Date(Date.now())
  closingTime.setHours(timeDate.getHours())
  closingTime.setMinutes(timeDate.getMinutes())
  closingTime.setSeconds(0)

  const timeDifference = closingTime.getTime() - now.getTime()

  const isSoon = timeDifference <= ALERT_CLOSURE_SOON_TIME
  const closureTime = `${doubleDigits(closingTime.getHours())}:${doubleDigits(closingTime.getMinutes())}`

  return { isSoon, closureTime }
}

export default isClosureSoon
