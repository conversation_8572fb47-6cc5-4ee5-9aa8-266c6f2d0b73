export const GENERIC_ERROR = 'GENERIC_ERROR'

const decodeMessage = message => {
  if (
    (typeof message === 'string' && message.startsWith('JSON')) ||
    (message && message.status && message.status >= 500)
  ) {
    return GENERIC_ERROR
  }

  if (message && message.status && message.status >= 400) {
    return (
      message.data &&
      message.data &&
      (message.data.result_message || message.data.result_detail)
    )
  }

  return message
}

const mapErrorMessage = messageCode => {
  const message = decodeMessage(messageCode)

  switch (message) {
  case 'ESTADOAVANCE_FINALIZADA_ERROR':
    return 'Ha ocurrido un error durante el procesamiento de su transacción. Intente nuevamente.'
  case 'Network request failed':
    return (
      'El sistema no esta disponible en este momento. ' +
        'Por favor inténtelo nuevamente más tarde.'
    )
  case 'BAD_EMAIL_ADDRESS':
    return 'Por favor ingresa un e-mail válido.'
  case 'INACTIVE_USER':
    return (
      'Usted no puede utilizar las máquinas porque su cuenta no está activa. ' +
        'Comuníquese con nosotros por más información.'
    )
  case 'INACTIVE_CARD':
    return (
      'No se puede obtener la información ya que la tarjeta esta deshabilitada. ' +
        'Comuníquese con nosotros por más información.'
    )
  case 'MACHINE_ACTIVATION_FAILED':
    return 'No se ha podido realizar la activación de la máquina. Por favor reintente más tarde.'
  case 'BUILDING_NOT_AVAILABLE':
    return (
      'Las máquinas de este edificio no estan disponibles en este momento. ' +
        'Comuníquese con nosotros por más información.'
    )
  case 'INVALID_USE_TIME':
    return (
      'Las máquina seleccionada no esta disponible en este horario. ' +
        'Por favor intente dentro del horario establecido.'
    )
  case 'OPERATION_CANCELED':
    return 'La operación ha sido cancelada, por favor retire el dinero.'
  case 'ESTADOAVANCE_CANCELADA':
  case 'OPERATION_CANCELED_CARD':
    return 'La operación ha sido cancelada, por favor retire su tarjeta.'
  case 'OPERATION_CANCELED_CARD_TIMEOUT':
    return 'La operación ha sido cancelada por falta de actividad, por favor retire su tarjeta.'
  case GENERIC_ERROR:
    return 'El sistema no ha podido procesar sus datos. Por favor inténtelo nuevamente.'
  case 'UNAUTHORIZED':
    return (
      'El sistema no ha podido inciar sesión con los datos ingresados. ' +
        'Por favor inténtelo nuevamente.'
    )
  case 'MACHINE_BUSY':
    return 'La máquina se encuentra en uso, espere por favor y reintente más tarde.'
  case 'MACHINE_ALREADY_BOOKED':
    return (
      'Lo sentimos, la máquina ya se encuentra en proceso de activción por otro usuario. ' +
        'Reintente con una diferente.'
    )
  default:
    return messageCode
  }
}

export default mapErrorMessage
