import { MAP_MODE_BUILDINGS, PERMISSIONS, ROLES } from '../constants'

class Permission {
  constructor(activation, showLastUserInfo) {
    this[PERMISSIONS.ACTIVATION] = !!activation
    this[PERMISSIONS.SHOW_LAST_USER_INFO] = !!showLastUserInfo
  }

  canActivate() {
    return this[PERMISSIONS.ACTIVATION]
  }

  canShowLastUserInfo() {
    return this[PERMISSIONS.SHOW_LAST_USER_INFO]
  }
}

const getPermissions = ({ role, showLastUserInfo, buildingId } = {}) => {
  if (role === ROLES.LAUNDROMAT) {
    return new Permission(true, showLastUserInfo)
  }

  // role === ROLES.BUILDING
  const showActivation = !MAP_MODE_BUILDINGS.includes(buildingId)
  return new Permission(showActivation, showLastUserInfo)
}

export default getPermissions
