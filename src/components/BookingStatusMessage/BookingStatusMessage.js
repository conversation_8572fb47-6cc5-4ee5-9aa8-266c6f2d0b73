import React from 'react'

export const STATUSES = {
  activationPendent: 'ACTIVATION_PENDENT',
  cancelled: 'CANCELLED',
  expired: 'EXPIRED',
  inProgress: 'IN_PROGRESS',
}

export const ENDING_STATUSES = [
  STATUSES.activationPendent,
  STATUSES.cancelled,
  STATUSES.expired,
]

const MESSAGES = {
  [STATUSES.activationPendent]:
    'El equipo seleccionado se encuentra disponible. ' +
    'Continue al siguiente paso por favor.',
  [STATUSES.cancelled]:
    'Lo sentimos ha ocurrido un error con el equipo. Reintente nuevamente',
  [STATUSES.expired]:
    'Lo sentimos ha ocurrido un error con el equipo. Reintente nuevamente',
  [STATUSES.inProgress]:
    'Aguarde mientras verificamos la disponibilidad del equipo...',
}

const BookingStatusMessage = ({ status, timeoutError }) => (
  <div>
    <h2>
      {MESSAGES[
        timeoutError ? STATUSES.expired : status || STATUSES.inProgress
      ] || ''}
    </h2>
  </div>
)

export default BookingStatusMessage
