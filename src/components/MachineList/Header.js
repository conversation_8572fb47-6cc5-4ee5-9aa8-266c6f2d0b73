import React from 'react'

import washerImage from 'assets/images/Lavadora-simbol.png'
import dryerImage from 'assets/images/Secadora-simbol.png'

const Header = ({ machine }) => {
  const isWasher = machine.machine_type === 'WASHER'

  return (
    <div className={`header ${isWasher ? 'washer' : 'dryer'}`}>
      <div>
        <img
          alt="Lavado"
          src={isWasher ? washerImage : dryerImage}
        />
      </div>
      <div className="type-box">{isWasher ? 'LAVADO' : 'SECADO'}</div>
    </div>
  )
}

export default Header
