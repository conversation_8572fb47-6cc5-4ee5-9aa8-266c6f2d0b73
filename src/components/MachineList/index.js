import React from 'react'
import { isEmpty } from 'lodash'
import { List } from 'antd'
import Countdown from 'react-countdown-now'

import Header from './Header'
import MachineImage from './MachineImage'
import InfoContainer from './InfoContainer'

const MachineList = ({
  data,
  onActivate,
  onCompleteUsage,
  onSelect,
  permissions,
}) => (
  <List
    className="list"
    dataSource={data || []}
    header={!isEmpty(data) && <Header machine={data[0]} />}
    itemLayout="horizontal"
    renderItem={item => {
      console.log(item.machine_sort_index, item.machine_remaning_time, new Date(Date.now() + item.machine_remaning_time).toISOString())

      const busy =
        item.machine_remaning_time > 0 ||
        (!item.availability && item.machine_remaning_time <= 0)
      return (
        <List.Item
          className={`machine ${busy ? 'in-use' : ''}`}
          onClick={() => (!busy ? onActivate(item) : onSelect(item))}
        >
          <List.Item.Meta
            avatar={(
              <MachineImage
                index={item.machine_sort_index}
                informative={permissions && permissions.canShowLastUserInfo()}
              />
            )}
            title={(
              <Countdown
                date={Date.now() + item.machine_remaning_time}
                onComplete={onCompleteUsage}
                renderer={obj => (
                  <InfoContainer
                    activable={permissions && permissions.canActivate()}
                    countdown={obj}
                    machine={item}
                  />
                )}
              />
            )}
          />
        </List.Item>
      )
    }}
    split={false}
  />
)

export default MachineList
