import React from 'react'

import { Icon } from 'antd'
import { pesoFormatter } from 'utils/formatters'
import { zeroPad } from 'react-countdown-now'

const InfoContainer = ({
  activable,
  countdown: { hours, minutes, seconds, completed },
  machine,
}) => {
  if (!machine.availability && machine.machine_remaning_time <= 0) {
    return (
      <div className="info-container__action">
        <span className="disabled">NO DISPONIBLE</span>
      </div>
    )
  }

  const statusClassName = completed ? 'active' : 'disabled'
  const getActionNameByStatus = () => {
    if (completed) {
      if (activable) {
        return 'Activar'
      }

      return 'Disponible'
    }

    return machine.inMaintenance ? 'En Mantenimiento' : 'En Uso'
  }

  console.log(hours, minutes, seconds)

  return (
    <div className="info-container">
      <div className={`info-container__header ${statusClassName}`}>
        {completed ? (
          <span>
            {machine.machine_capacity} kg
            <Icon className="caret-right-icon" type="caret-right" />
            {pesoFormatter(machine.machine_rate)}
          </span>
        ) : (
          <div className="timer">
            <Icon className="clock-circle-icon" type="clock-circle" />

            <span>
              - {hours ? `${zeroPad(hours)}:` : ''}
              {zeroPad(minutes)}:{zeroPad(seconds)}
            </span>
          </div>
        )}
      </div>
      <div className="info-container__action">
        <span className={statusClassName}>{getActionNameByStatus()}</span>
      </div>
    </div>
  )
}

export default InfoContainer
