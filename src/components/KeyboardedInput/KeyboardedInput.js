import React, { useRef } from 'react'
import PropTypes from 'prop-types'

import KeyboardedInputComp from 'react-touch-screen-keyboard'

import * as KEYBOARDS from './Keyboards'

/**
 * === react-touch-screen-keyboard documentation ===
 *
 * Examples:
 * https://github.com/xtrinch/react-touch-screen-keyboard/blob/master/examples/index.js
 *
 * Component definition:
 * https://github.com/xtrinch/react-touch-screen-keyboard/blob/master/src/KeyboardedInput.js
 *
 */

const KeyboardedInput = ({
  capitalize = true,
  draggable = false,
  enabled = true,
  forwardedRef,
  keyboard = KEYBOARDS.DEFAULT_KEY,
  titleize = false,
  ...props
}) => {
  const ref = useRef()

  return (
    <KeyboardedInputComp
      ref={forwardedRef || ref}
      defaultKeyboard={
        KEYBOARDS.NAMES.includes(keyboard)
          ? KEYBOARDS.MAP[keyboard]
          : KEYBOARDS.DEFAULT
      }
      enabled={enabled}
      isDraggable={draggable}
      isFirstLetterUppercase={capitalize}
      uppercaseAfterSpace={titleize}
      {...props}
    />
  )
}

KeyboardedInput.propTypes = {
  capitalize: PropTypes.bool,
  draggable: PropTypes.bool,
  enabled: PropTypes.bool,
  forwardedRef: PropTypes.object,
  keyboard: PropTypes.oneOf(KEYBOARDS.NAMES),
  onBlur: PropTypes.func,
  titleize: PropTypes.bool,
  showSpacebar: PropTypes.bool,
}

export default React.forwardRef((props, ref) => (
  <KeyboardedInput forwardedRef={ref} {...props} />
))
