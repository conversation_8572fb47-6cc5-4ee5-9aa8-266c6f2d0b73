import React, { Component } from 'react'
import { connect } from 'react-redux'
import { Redirect } from 'react-router-dom'
import { Row, Col, Button, Icon } from 'antd'
import {
  cancelTransactionPOS,
  initTransactionPOS,
  checkTransactionPOS,
  completeTransactionPOS,
} from 'redux/actions/payment'
import ErrorMessage from 'components/ErrorMessage'
import withMessage from '../hocs/MessageHOC'
import withLoading from '../hocs/LoadingHOC'
import { MAX_TRIES_POS_CHECK } from '../../constants'

import PosIcon from '../../assets/images/pos.png'

const Loading = withLoading('div')
const MessageAndLoading = withMessage(Loading)

class CardPayment extends Component {
  constructor(props) {
    super(props)
    this.state = {
      backToMain: false,
    }
  }

  componentDidMount = () => {
    const {
      selectedMachine,
      initTransaction,
      totem: { token },
      billingInfo,
    } = this.props
    initTransaction(selectedMachine, token, billingInfo)
  };

  componentDidUpdate = () => {
    const { timer } = this.state
    const {
      checkOperation,
      completeTransaction,
      checkingOperation,
      cashCollected,
      transactionEnded,
      transaction,
      totem: { token },
      ending,
      tries,
    } = this.props

    if (transaction && !checkingOperation && !timer) {
      const checkOperationTimer = () =>
        checkOperation(transaction.token, token)
      // eslint-disable-next-line react/no-did-update-set-state
      this.setState({ timer: setInterval(checkOperationTimer, 5000) })
    }
    tries > MAX_TRIES_POS_CHECK &&
      this.cancelOperation(
        `Max tries exceeded: ${tries} of ${MAX_TRIES_POS_CHECK}`,
      )
    !transactionEnded && !ending && cashCollected && clearInterval(timer)
    !transactionEnded &&
      transaction &&
      !ending &&
      cashCollected &&
      completeTransaction(transaction.id, token)
  };

  cancelOperation = reason => {
    const { timer } = this.state
    const {
      cancelOperation,
      transaction,
      totem: { token },
    } = this.props
    console.log(reason)
    cancelOperation(transaction.id, transaction.token, token)
    clearInterval(timer)
  };

  render() {
    const { backToMain } = this.state
    const {
      transactionEnded,
      isFetching,
      errors,
      selectedMachine: { machine_type, machine_sort_index },
    } = this.props
    return (
      <MessageAndLoading
        error={errors}
        isFetching={isFetching}
        message={errors}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          {errors && (
            <Col span={20}>
              <ErrorMessage hideBackButton message={errors} />
              <Button onClick={() => this.setState({ backToMain: true })}>
                Volver
              </Button>
            </Col>
          )}
          {!errors && (
            <>
              <Row className="pos" justify="center" type="flex">
                <h1>
                  Estás activando la{' '}
                  {machine_type === 'WASHER' ? 'LAVADORA' : 'SECADORA'} Nº{' '}
                  {machine_sort_index}
                </h1>
                <h2>
                  Insertá tu tarjeta en el POS ubicado junto a esta pantalla e
                  ingresá tu PIN.
                </h2>
              </Row>
              <Row justify="center" type="flex">
                <Col span={22}>
                  <img alt="POS" className="logo" src={PosIcon} />
                </Col>
              </Row>
            </>
          )}
          {!transactionEnded && (
            <Button
              onClick={() => this.cancelOperation('Cancel click')}
              type="link"
            >
              <Icon type="stop" />
              Cancelar operación
            </Button>
          )}
          {backToMain && (
            <Redirect
              push
              to={{
                pathname: '/',
              }}
            />
          )}
        </div>
      </MessageAndLoading>
    )
  }
}

function mapStateToProps(state) {
  return {
    ...state.paymentReducer,
    totem: state.totemReducer,
  }
}

function mapDispatchToProps(dispatch) {
  return {
    initTransaction: (machine, token, billingInfo) =>
      dispatch(initTransactionPOS(token, machine, billingInfo)),
    checkOperation: (transactionToken, token) =>
      dispatch(checkTransactionPOS(transactionToken, token)),
    cancelOperation: (transactionId, transactionToken, token) =>
      dispatch(cancelTransactionPOS(transactionId, transactionToken, token)),
    completeTransaction: (transactionId, token) =>
      dispatch(completeTransactionPOS(transactionId, token)),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(CardPayment)
