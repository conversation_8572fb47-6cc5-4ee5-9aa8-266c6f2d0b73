import React from 'react'

import { Row, Col, Button } from 'antd'

import mapErrorMessage, { GENERIC_ERROR } from 'utils/mappers'

const ErrorFallback = () => (
  <Row className="contentMain" justify="center" type="flex">
    <Col span={20}>
      <h1>{mapErrorMessage(GENERIC_ERROR)}</h1>

      <Button
        onClick={() => {
          window.location.href = '/'
        }}
        type="primary"
      >
        Volver al Inicio
      </Button>
    </Col>
  </Row>
)

export default ErrorFallback
