import React, { Component } from 'react'
import { Modal } from 'antd'

class ConfirmationModal extends Component {
  constructor(props) {
    super(props)
    this.state = { visible: true }
  }

  onClose = () => {
    const { onClose } = this.props
    onClose && onClose()
    this.setState({ visible: false })
  };

  onOk = () => {
    const { onOk } = this.props
    onOk && onOk()
    this.setState({ visible: false })
  };

  render() {
    const { title, message, okText, closeText } = this.props
    const { visible } = this.state
    return (
      <Modal
        cancelText={closeText}
        okText={okText}
        onCancel={this.onClose}
        onOk={this.onOk}
        title={title}
        visible={visible}
      >
        <p>{message}</p>
      </Modal>
    )
  }
}
export default ConfirmationModal
