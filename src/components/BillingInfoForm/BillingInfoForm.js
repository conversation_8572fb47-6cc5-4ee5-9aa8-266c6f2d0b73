import React, { useRef, useState } from 'react'
import { Form, Input, But<PERSON>, Row, Col } from 'antd'

import KeyboardedInput from 'components/KeyboardedInput'

import classes from './BillingInfoForm.module.scss'

const formItemLayout = {
  labelCol: {
    xs: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 18 },
  },
}

const FIELDS = {
  RUT: 'rut',
  CUSTOMER_NAME: 'customerName',
  CUSTOMER_ADDRESS: 'customerAddress',
  EMAIL: 'email',
}

/* eslint-disable max-len */
const BUSINESS_ERROR_MESSAGES = {
  REQUIRED_RUT:
    'Debe ingresar el RUT de su negocio! En caso de que no lo desee, borrar los campos de esta sección.',
  REQUIRED_CUSTOMER_NAME:
    'Debe ingresar la Razón Social de su negocio! En caso de que no lo desee, borrar los campos de esta sección.',
  REQUIRED_CUSTOMER_ADDRESS:
    'Debe ingresar la Dirección de su negocio! En caso de que no lo desee, borrar los campos de esta sección.',
}
/* eslint-enable max-len */

const BillingInfoForm = ({ form, onSubmit, submittionDisabled }) => {
  const keyboard = useRef()
  const { getFieldDecorator } = form
  const [{ selected, ...formValues }, setValues] = useState({
    [FIELDS.RUT]: '',
    [FIELDS.CUSTOMER_NAME]: '',
    [FIELDS.CUSTOMER_ADDRESS]: '',
    [FIELDS.EMAIL]: '',
    selected: '',
  })

  const setSelected = option => () => {
    setValues({ ...formValues, selected: option })
    if (keyboard && keyboard.current) {
      keyboard.current.focus()
    }
  }
  const setFormValue = text => {
    setValues({ selected, ...formValues, [selected]: text })
    form.setFieldsValue({ [selected]: text })
  }
  const handleSubmit = e => {
    e.preventDefault()
    form.validateFields((err, values) => !err && onSubmit(values))
  }

  return (
    <Form
      onSubmit={handleSubmit}
      {...formItemLayout}
      className={classes.billingInfoForm}
    >
      <Row justify="center" type="flex">
        <Col span={20}>
          <h2>Necesita incluir el RUT en su factura?</h2>
          <p className="lm-color--white">
            <em>* opcional</em>
          </p>

          <Form.Item label="RUT">
            {getFieldDecorator(FIELDS.RUT, {
              rules: [
                {
                  len: 12,
                  message: 'Ingrese su RUT de 12 dígitos.',
                },
                {
                  validator: (rule, value, callback) => {
                    if (
                      !value &&
                      (form.getFieldValue(FIELDS.CUSTOMER_NAME) ||
                        form.getFieldValue(FIELDS.CUSTOMER_ADDRESS))
                    ) {
                      callback(BUSINESS_ERROR_MESSAGES.REQUIRED_RUT)
                    } else {
                      callback()
                    }
                  },
                },
              ],
            })(
              <Input
                onFocus={setSelected(FIELDS.RUT)}
                placeholder="RUT"
                size="large"
              />,
            )}
          </Form.Item>
          <Form.Item label="Razón Social">
            {getFieldDecorator(FIELDS.CUSTOMER_NAME, {
              rules: [
                {
                  validator: (rule, value, callback) => {
                    if (
                      !value &&
                      (form.getFieldValue(FIELDS.RUT) ||
                        form.getFieldValue(FIELDS.CUSTOMER_ADDRESS))
                    ) {
                      callback(BUSINESS_ERROR_MESSAGES.REQUIRED_CUSTOMER_NAME)
                    } else {
                      callback()
                    }
                  },
                },
              ],
            })(
              <Input
                onFocus={setSelected(FIELDS.CUSTOMER_NAME)}
                placeholder="Razón Social"
                size="large"
              />,
            )}
          </Form.Item>
          <Form.Item label="Dirección">
            {getFieldDecorator(FIELDS.CUSTOMER_ADDRESS, {
              rules: [
                {
                  validator: (rule, value, callback) => {
                    if (
                      !value &&
                      (form.getFieldValue(FIELDS.RUT) ||
                        form.getFieldValue(FIELDS.CUSTOMER_NAME))
                    ) {
                      callback(
                        BUSINESS_ERROR_MESSAGES.REQUIRED_CUSTOMER_ADDRESS,
                      )
                    } else {
                      callback()
                    }
                  },
                },
              ],
            })(
              <Input
                onFocus={setSelected(FIELDS.CUSTOMER_ADDRESS)}
                placeholder="Dirección"
                size="large"
              />,
            )}
          </Form.Item>
        </Col>
      </Row>

      <Row justify="center" type="flex">
        <Col span={20}>
          <h2>Desea recibir la factura por e-mail?</h2>

          <Form.Item label="E-mail">
            {getFieldDecorator(FIELDS.EMAIL, {
              rules: [
                {
                  type: 'email',
                  message: 'Ingrese un e-mail válido.',
                },
              ],
            })(
              <Input
                onFocus={setSelected(FIELDS.EMAIL)}
                placeholder="E-mail"
                size="large"
              />,
            )}
          </Form.Item>
        </Col>
      </Row>

      <Row className="lm-mt" justify="center" type="flex">
        <Form.Item>
          <Button
            disabled={submittionDisabled}
            htmlType="submit"
            type="primary"
          >
            Continuar
          </Button>
        </Form.Item>
      </Row>

      <KeyboardedInput
        ref={keyboard}
        inputClassName="lm-hidden"
        keyboard={
          { [FIELDS.RUT]: 'numbers', [FIELDS.EMAIL]: 'email' }[selected]
        }
        onChange={setFormValue}
        showSpacebar={[FIELDS.CUSTOMER_NAME, FIELDS.CUSTOMER_ADDRESS].includes(
          selected,
        )}
        value={formValues[selected]}
      />
    </Form>
  )
}

export default Form.create({ name: 'billing_info' })(BillingInfoForm)
