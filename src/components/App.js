import React, { Component } from 'react'
import { Provider } from 'react-redux'
import { Router, Route, Switch } from 'react-router-dom'
import PrivateRoute from 'components/PrivateRoute'
import Authentication from 'screens/Authentication'
import history from 'navigation/history'

import LavomatApplicationState from 'redux/store'

import LaundromatMap from 'screens/LaundromatMap'
import MainPage from 'screens/MainPage'
import PaymentMethod from 'screens/PaymentMethod'
import ViewReceipt from 'screens/ViewReceipt'
import Payment from 'screens/Payment'

import ErrorMessage from './ErrorMessage'
import Version from './Version'

class App extends Component {
  constructor(props) {
    super(props)
    this.appState = new LavomatApplicationState()
  }

  render() {
    return (
      <Provider store={this.appState.store}>
        <Router history={history}>
          <Switch>
            <PrivateRoute
              exact
              path="/"
              render={props => (
                <MainPage
                  mainContent={<LaundromatMap {...props} history={history} />}
                />
              )}
            />
            <Route
              exact
              path="/sign-in"
              render={props => (
                <MainPage
                  mainContent={<Authentication {...props} history={history} />}
                />
              )}
            />
            <PrivateRoute
              exact
              path="/payment"
              render={props => (
                <MainPage
                  enableBack
                  history={history}
                  mainContent={<PaymentMethod {...props} />}
                />
              )}
            />
            <PrivateRoute
              exact
              path="/payment/pay"
              render={props => (
                <MainPage
                  history={history}
                  mainContent={<Payment {...props} />}
                />
              )}
            />
            <PrivateRoute
              exact
              path="/payment/cancel"
              render={props => {
                const { location } = props
                const { timeout, useCard } = location ? location.state : {}
                return (
                  <MainPage
                    history={history}
                    mainContent={(
                      <ErrorMessage
                        message={`OPERATION_CANCELED${useCard ? '_CARD' : ''}${
                          timeout ? '_TIMEOUT' : ''
                        }`}
                        {...props}
                      />
                    )}
                  />
                )
              }}
            />
            <PrivateRoute
              exact
              path="/payment/receipt"
              render={props => (
                <MainPage mainContent={<ViewReceipt {...props} />} />
              )}
            />
          </Switch>
        </Router>
        <Version />
      </Provider>
    )
  }
}

export default App
