import React, { useRef, useState } from 'react'
import { Form, Input, Button, Icon } from 'antd'

import KeyboardedInput from 'components/KeyboardedInput'

import { COMPANY_EMAIL_DOMAIN } from '../../constants'

import classes from './styles.module.scss'

const LoginForm = ({ form, onSubmit }) => {
  const keyboard = useRef()
  const { getFieldDecorator } = form
  const [{ selected, ...formValues }, setValues] = useState({
    username: '',
    password: '',
    selected: 'username',
  })

  const setSelected = option => () => {
    setValues({ ...formValues, selected: option })
    if (keyboard && keyboard.current) {
      keyboard.current.focus()
    }
  }
  const setFormValue = text => {
    setValues({ selected, ...formValues, [selected]: text })
    form.setFieldsValue({ [selected]: text })
  }
  const handleSubmit = e => {
    e.preventDefault()
    form.validateFields((err, values) => !err && onSubmit(values))
  }

  return (
    <Form className={classes.loginForm} onSubmit={handleSubmit}>
      <Form.Item>
        {getFieldDecorator('username', {
          rules: [{ required: true, message: 'Please input your Username!' }],
        })(
          <Input
            addonAfter={COMPANY_EMAIL_DOMAIN}
            onFocus={setSelected('username')}
            placeholder="Username"
            prefix={<Icon type="robot" />}
            size="large"
          />,
        )}
      </Form.Item>
      <Form.Item>
        {getFieldDecorator('password', {
          rules: [{ required: true, message: 'Please input your Password!' }],
        })(
          <Input.Password
            onFocus={setSelected('password')}
            placeholder="Password"
            prefix={<Icon type="lock" />}
            size="large"
            type="password"
          />,
        )}
      </Form.Item>
      <Form.Item>
        <Button
          className={classes.loginFormSubmit}
          htmlType="submit"
          type="primary"
        >
          Log in
        </Button>
      </Form.Item>

      <KeyboardedInput
        ref={keyboard}
        capitalize={false}
        inputClassName={classes.keyboard}
        onChange={setFormValue}
        value={formValues[selected]}
      />
    </Form>
  )
}

export default Form.create({ name: 'normal_login' })(LoginForm)
