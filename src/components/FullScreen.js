import React, { Component } from 'react'
import { connect } from 'react-redux'
import { Button, Icon } from 'antd'

import KeyboardedInput from 'components/KeyboardedInput'
import { clearData } from 'redux/actions/totem'
import { LOG_OUT_PASSWORD, EXIT_FULL_SCREEN_PASWORD } from '../constants'

class FullScreen extends Component {
  constructor(props) {
    super(props)
    this.state = { pin: '' }
  }

  render() {
    const { showPassword, pin } = this.state
    const { logOut } = this.props

    const handlePinSubmit = () => {
      try {
        if (pin === EXIT_FULL_SCREEN_PASWORD) {
          if (!document.webkitIsFullScreen) {
            document.documentElement.requestFullscreen()
              .catch(err => console.log('requestFullscreen', err))
          } else {
            document.exitFullscreen()
              .catch(err => console.log('exitFullscreen', err))
          }
        }

        if (pin === LOG_OUT_PASSWORD) {
          logOut()
        }

        this.setState({ showPassword: false, pin: '' })
      } catch (err) {
        console.log('handlePinSubmit', err)
      }
    }

    return (
      <div>
        {showPassword && (
          <KeyboardedInput
            capitalize={false}
            inputClassName="keyboard small"
            onBlur={handlePinSubmit}
            onChange={text => this.setState({ pin: text })}
            type="password"
            value={pin}
          />
        )}
        {!showPassword && (
          <Button
            className="transparent"
            onClick={() => this.setState({ showPassword: true })}
          >
            <Icon type="fullscreen" />
          </Button>
        )}
      </div>
    )
  }
}


export default connect(
  () => ({}),
  dispatch => ({
    logOut: () => dispatch(clearData()),
  }),
)(FullScreen)
