import React from 'react'
import { But<PERSON> } from 'antd'
import mapErrorMessage, { GENERIC_ERROR } from 'utils/mappers'

const ErrorMessage = ({ message = GENERIC_ERROR, hideBackButton, history }) => (
  <div>
    <div>
      <h1>{message && mapErrorMessage(message)}</h1>
    </div>
    <div>
      {!hideBackButton && (
        <Button
          onClick={() => {
            history.push('/')
          }}
          type="primary"
        >
          Volver
        </Button>
      )}
    </div>
  </div>
)

export default ErrorMessage
