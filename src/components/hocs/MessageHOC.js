import React from 'react'

const displayMessage = (success, textMessage) => {
  // eslint-disable-next-line no-alert
  alert(textMessage)
}

const withMessage = Comp => ({ children, error, success, message, onOk, ...props }) => (
  <div style={{ flex: 1 }}>
    {!!error && displayMessage(false, message)}
    {!!success && displayMessage(true, message)}
    <Comp {...props}>{children}</Comp>
  </div>
)

export default withMessage
