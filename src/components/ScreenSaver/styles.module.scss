.screenSaver {
  align-items: center;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 9999;
}

.logo {
  max-width: 100%;
}

.screenSaverImage {
  max-width: 100%;
  width: 100%;
}

.touchText {
  animation-duration: 3s;
  animation-iteration-count: infinite;
  animation-name: floating;
  animation-timing-function: ease-in-out;
  bottom: 10%;
  color: white;
  font-family: 'MPLUSRounded1c-Regular';
  margin-left: 30px;
  margin-top: 5px;
  position: absolute;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
}

@keyframes floating {
  0% {
    transform: translate(0, 0);
  }

  50% {
    transform: translate(0, 15px);
  }

  100% {
    transform: translate(0, -0);
  }
}
