import React, { useState, useEffect, useRef } from 'react'
import PropTypes from 'prop-types'

import TexturaConLogo from '../../assets/images/TexturaConLogo.jpg'

import classes from './styles.module.scss'

const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart']

const ScreenSaver = ({ onUnload, timeout }) => {
  const [screenSaverActive, setScreenSaverActive] = useState(false)
  const screenSaverTimer = useRef()

  const resetScreenSaverTimer = () => {
    if (screenSaverActive) {
      setScreenSaverActive(false)
      if (onUnload) {
        onUnload()
      }
    }
    clearTimeout(screenSaverTimer.current)
    screenSaverTimer.current = setTimeout(
      () => setScreenSaverActive(true),
      timeout,
    )
  }

  useEffect(() => {
    events.forEach(_event => {
      window.addEventListener(_event, resetScreenSaverTimer)
    })

    screenSaverTimer.current = setTimeout(
      () => setScreenSaverActive(true),
      timeout,
    )

    return () => {
      events.forEach(_event => {
        window.removeEventListener(_event, resetScreenSaverTimer)
      })
      clearTimeout(screenSaverTimer.current)
    }
  }, [resetScreenSaverTimer, screenSaverActive])

  return (
    <>
      {screenSaverActive ? (
        <div className={classes.screenSaver}>
          <img
            alt="LAVOMAT logo"
            className={classes.screenSaverImage}
            src={TexturaConLogo}
          />
          <h1 className={classes.touchText}>
            Toca en cualquier lugar para continuar
          </h1>
        </div>
      ) : null}
    </>
  )
}

ScreenSaver.propTypes = {
  onUnload: PropTypes.func,
  timeout: PropTypes.number.isRequired,
}

export default ScreenSaver
