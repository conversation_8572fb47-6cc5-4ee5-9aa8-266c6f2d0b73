import React from 'react'
import { Route, Redirect } from 'react-router-dom'
import { connect } from 'react-redux'
import PropTypes from 'prop-types'

const PrivateRoute = ({ render, token: authenticated, ...props }) => (
  <Route
    {...props}
    render={innerProps =>
      authenticated ? render(innerProps) : <Redirect to="/sign-in" />
    }
  />
)

PrivateRoute.propTypes = {
  render: PropTypes.func.isRequired,
}

const mapStateToProps = state => state.totemReducer

export default connect(
  mapStateToProps,
)(PrivateRoute)
