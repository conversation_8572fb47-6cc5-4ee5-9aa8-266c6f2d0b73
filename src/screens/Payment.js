import React, { Component } from 'react'
import { connect } from 'react-redux'
import { Redirect, with<PERSON>outer } from 'react-router-dom'
import { Row, Col, Button, Icon } from 'antd'

import CardPayment from 'components/payment/CardPayment'
import ErrorMessage from 'components/ErrorMessage'
import ScreenSaverWrapper from 'components/ScreenSaver'

import { OTHERS_SCREEN_SAVER_TIMEOUT } from '../constants'

/*
 * path: /payment/pay
 */
class Payment extends Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  componentDidUpdate() {
    const { errors } = this.props
    errors && this.clearIntervals()
  }

  clearIntervals = () => {
    let id = window.setTimeout(() => {}, 0)

    while (id) {
      window.clearTimeout(id)
      id -= 1
    }
  };

  navigateAndClearState = () => {
    this.setState({})
    this.props.history.push('/')
  };

  render() {
    const { backToMain } = this.state
    const {
      transactionEnded,
      paymentCanceled,
      errors,
      ending,
      timeout,
      location: {
        state: { useCard, rut, customerName, customerAddress, email },
      },
    } = this.props

    return (
      <>
        <ScreenSaverWrapper
          onUnload={this.navigateAndClearState}
          timeout={OTHERS_SCREEN_SAVER_TIMEOUT}
        />
        <Row className="payment" justify="center" type="flex">
          {errors && (
            <Col span={20}>
              <ErrorMessage hideBackButton message={errors} />
              <Button onClick={() => this.setState({ backToMain: true })}>
                Volver
              </Button>
            </Col>
          )}
          {!errors && (
            <Col span={24}>
              {useCard && (
                <CardPayment
                  billingInfo={{ customerName, customerAddress, rut }}
                />
              )}
            </Col>
          )}
          {ending && (
            <Icon style={{ color: '#fff', fontSize: 32 }} type="loading" />
          )}
          {!errors && paymentCanceled && (
            <Redirect
              push
              to={{
                pathname: '/payment/cancel',
                state: { timeout, useCard },
              }}
            />
          )}
          {transactionEnded && (
            <Redirect
              push
              to={{
                pathname: '/payment/receipt',
                state: { email },
              }}
            />
          )}
          {backToMain && (
            <Redirect
              push
              to={{
                pathname: '/',
              }}
            />
          )}
        </Row>
      </>
    )
  }
}

function mapStateToProps(state) {
  return state.paymentReducer
}

const PaymentWithRouter = withRouter(Payment)
export default connect(mapStateToProps)(PaymentWithRouter)
