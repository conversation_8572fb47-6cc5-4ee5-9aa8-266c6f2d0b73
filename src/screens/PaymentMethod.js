import React, { Component } from 'react'
import { connect } from 'react-redux'
import { Redirect, with<PERSON><PERSON><PERSON> } from 'react-router-dom'
import { Row, Col } from 'antd'
import { saveSelectedMachine as saveSelectedMachineAction } from 'redux/actions/payment'
import {
  bookMachine as bookMachineAction,
  getBookingMachine as getBookingMachineAction,
  cancelBookingMachine as cancelBookingMachineAction,
} from 'redux/actions/machine'

import BillingInfoForm from 'components/BillingInfoForm/BillingInfoForm'
import BookingStatusMessage, {
  STATUSES as BOOKING_STATUSES,
  ENDING_STATUSES as BOOKING_ENDING_STATUSES,
} from 'components/BookingStatusMessage'
import ScreenSaverWrapper from 'components/ScreenSaver'
import ErrorMessage from 'components/ErrorMessage'

import { OTHERS_SCREEN_SAVER_TIMEOUT } from '../constants'

// 20 seconds => 1 attempt per second
const BOOKING_TIMEOUT_LIMIT = 20

const initialState = {
  useCard: false,
  rut: '',
  customerName: '',
  customerAddress: '',
  email: '',
}
/*
 * path: /payment
 */
class PaymentMethod extends Component {
  constructor(props) {
    super(props)
    this.state = initialState
    this.keyboard = React.createRef()
  }

  componentDidMount = () => {
    const {
      saveMachine,
      bookMachine,
      totem: { token },
      location: {
        state: { selectedMachine },
      },
    } = this.props
    saveMachine(selectedMachine)
    bookMachine(
      { serial: !!selectedMachine && selectedMachine.machine_serial },
      token,
    )
  };

  componentDidUpdate = () => {
    const {
      getBookingMachine,
      machine: {
        isFetching,
        bookingId,
        bookingStatus,
        bookingStatusError,
        bookingStatusTries,
      },
      totem: { token },
    } = this.props
    const { bookingStatusIntervalId } = this.state

    if (
      !isFetching &&
      !bookingStatusIntervalId &&
      bookingId &&
      !bookingStatus &&
      !bookingStatusError
    ) {
      // initialize interval if the booking was already made
      const checkBookingMachineStatus = () =>
        getBookingMachine({ id: bookingId }, token)

      this.setState({
        bookingStatusIntervalId: setInterval(checkBookingMachineStatus, 1000),
      })
    } else if (
      bookingStatusIntervalId &&
      (bookingStatusError ||
        BOOKING_ENDING_STATUSES.includes(bookingStatus) ||
        bookingStatusTries > BOOKING_TIMEOUT_LIMIT)
    ) {
      // cancel interval in case of:
      // - failure,
      // - getting a response
      // - or waited more than 20"
      clearInterval(bookingStatusIntervalId)
    }
  };

  componentWillUnmount = () => {
    const { bookingStatusIntervalId } = this.state

    if (bookingStatusIntervalId) {
      clearInterval(bookingStatusIntervalId)

      if (!this.props.cancelling && !this.state.redirectToPayment) {
        const {
          cancelBookingMachine,
          machine: { bookingId: id },
          totem: { token },
        } = this.props
        console.log('cancelling')
        cancelBookingMachine({ id }, token)
      }
    }
  };

  navigateAndClearState = () => {
    this.setState(initialState)
    this.props.history.push('/')
  };

  render() {
    const {
      redirectToPayment,
      useCard,
      rut,
      customerName,
      email,
      customerAddress,
    } = this.state
    const {
      machine: {
        isFetching,
        bookingId,
        bookingError,
        bookingStatus,
        bookingStatusError,
        bookingStatusTries,
      },
    } = this.props

    const canContinue =
      !isFetching &&
      bookingId &&
      !bookingError &&
      !bookingStatusError &&
      bookingStatus === BOOKING_STATUSES.activationPendent

    return (
      <>
        <ScreenSaverWrapper
          onUnload={this.navigateAndClearState}
          timeout={OTHERS_SCREEN_SAVER_TIMEOUT}
        />
        <Row className="payment" justify="center" type="flex">
          <Col span={20}>
            <BillingInfoForm
              onSubmit={values =>
                this.setState({
                  ...values,
                  useCard: true,
                  redirectToPayment: true,
                })
              }
              submittionDisabled={!canContinue}
            />

            <BookingStatusMessage
              status={bookingStatus}
              timeoutError={bookingStatusTries > BOOKING_TIMEOUT_LIMIT}
            />
            <ErrorMessage
              hideBackButton
              message={bookingError || bookingStatusError}
            />
          </Col>
          {redirectToPayment && (
            <Redirect
              push
              to={{
                pathname: '/payment/pay',
                state: { useCard, rut, customerName, email, customerAddress },
              }}
            />
          )}
        </Row>
      </>
    )
  }
}

function mapStateToProps(state) {
  return {
    ...state.paymentReducer,
    totem: state.totemReducer,
    machine: state.machineReducer,
  }
}

function mapDispatchToProps(dispatch) {
  return {
    saveMachine: selectedMachine => {
      dispatch(saveSelectedMachineAction(selectedMachine))
    },
    bookMachine: (selectedMachine, token) =>
      dispatch(bookMachineAction(selectedMachine, token)),
    getBookingMachine: (selectedMachine, token) =>
      dispatch(getBookingMachineAction(selectedMachine, token)),
    cancelBookingMachine: (selectedMachine, token) =>
      cancelBookingMachineAction(selectedMachine, token),
  }
}

const PaymentMethodWithRouter = withRouter(PaymentMethod)
export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(PaymentMethodWithRouter)
