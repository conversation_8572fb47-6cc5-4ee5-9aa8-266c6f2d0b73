import React from 'react'
import { Redirect } from 'react-router-dom'
import { connect } from 'react-redux'
import { authenticateTotem } from 'redux/actions/totem'

import { Col, Row } from 'antd'

import LoginForm from 'components/LoginForm'
import ErrorMessage from 'components/ErrorMessage'

const Authentication = ({ authenticate, token, errors }) => {
  if (token) return (<Redirect to="/" />)

  return (
    <Row justify="center" type="flex">
      <Col span={10}>
        <LoginForm onSubmit={values => authenticate(values)} />

        {errors && (
          <Col>
            <ErrorMessage
              hideBackButton
              message={errors}
            />
          </Col>
        )}
      </Col>
    </Row>
  )
}

export default connect(
  state => state.totemReducer,
  dispatch => ({
    authenticate: credentials => dispatch(authenticateTotem(credentials)),
  }),
)(Authentication)

