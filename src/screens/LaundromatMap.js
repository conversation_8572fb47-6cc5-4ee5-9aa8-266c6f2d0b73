import React, { Component } from 'react'
import { connect } from 'react-redux'
import { Redirect } from 'react-router-dom'
import { Row, Col, Modal } from 'antd'
import { filter, map, isEmpty } from 'lodash'

import MachineList from 'components/MachineList'
import ConfirmationModal from 'components/ConfimationModal'
import withMessage from 'components/hocs/MessageHOC'
import withLoading from 'components/hocs/LoadingHOC'
import ErrorMessage from 'components/ErrorMessage'
import ScreenSaverWrapper from 'components/ScreenSaver'

import { getMachinesAvailability as getMachinesAvailabilityAction } from 'redux/actions/machine'
import { getBuildingInfo } from 'redux/actions/totem'
import getPermissions from 'utils/getPermissions'
import isClosureSoon from 'utils/isClosureSoon'

import {
  SCREEN_REFRESH_TIME,
  LAUNDROMAT_MAP_SCREEN_SAVER_TIMEOUT,
} from '../constants'

const Loading = withLoading('div')
const MessageAndLoading = withMessage(Loading)

const initialState = {
  activationSent: false,
  showModal: false,
  showError: false,
  activated: null,
  intervalId: 0,
  permissions: null,
}

/*
 * path: /
 */
class LaundromatMap extends Component {
  constructor(props) {
    super(props)
    this.state = initialState
  }

  componentDidMount = () => {
    this.blockGoBackAction()
    const {
      isFetching,
      totem: { token },
      totem,
    } = this.props

    this.setState({ permissions: getPermissions(totem) })
    if (token && !isFetching) {
      this.refreshMachineStatus()
    }

    if (token) {
      this.props.getBuilding(token)
    }
  };

  componentDidUpdate = () => {
    const {
      machines,
      isFetching,
      totem: { token },
    } = this.props
    const { intervalId } = this.state
    if (token && !machines && !isFetching && !intervalId) {
      this.refreshMachineStatus()
    }
    if (machines && !intervalId) {
      this.setState({
        intervalId: setInterval(this.refreshMachineStatus, SCREEN_REFRESH_TIME),
      })
    }
  };

  createMachinesList = machines => {
    const indexedMachines = map(machines, (machine, index) => ({
      ...machine,
      index: index + 1,
    }))
    const washers = filter(
      indexedMachines,
      machine => machine.machine_type === 'WASHER',
    )
    const dryers = filter(
      indexedMachines,
      machine => machine.machine_type === 'DRYER',
    )
    return { washers, dryers }
  };

  updateMachineStatus = () => {
    const {
      getMachinesAvailability,
      totem: { token, buildingId },
    } = this.props
    getMachinesAvailability(token, buildingId)
  };

  refreshMachineStatus = () => {
    if (this.props.isFetching) return

    this.updateMachineStatus()
  };

  refreshMachineStatusAfterDone = () => {
    this.updateMachineStatus()
  };

  blockGoBackAction = () => {
    this.props.history.block(
      (location, action) =>
        !(action === 'POP' && location.pathname === '/payment/receipt'),
    )
  };

  onActivateMachine = machine => {
    if (this.state.permissions && this.state.permissions.canActivate()) {
      const activate = () => {
        this.setState({ selectedMachine: machine, redirect: true })
      }

      const time = !!(this.props.totem) ? this.props.totem.closingTime : null
      const { isSoon, closureTime } = isClosureSoon(time)
      if (isSoon) {
        Modal.confirm({
          title: 'Alerta de cierre',
          content: `El local cerrará sus puertas a las ${closureTime}. ¿Está seguro de que desea continuar?`,
          onOk: activate,
        })
      } else {
        activate()
      }
    } else {
      this.onSelectMachine(machine)
    }
  };

  onSelectMachine = machine => {
    if (
      this.state.permissions &&
      this.state.permissions.canShowLastUserInfo()
    ) {
      Modal.info({
        title: 'Información del último uso',
        content:
          (machine && machine.machine_last_user_info) ||
          'Información no disponible en este momento. Intente más tarde.',
      })
    }
  };

  render() {
    const { isBuildingAvailable, errors, machines, isFetching } = this.props
    const { showMessage, showModal, selectedMachine, redirect, permissions } =
      this.state

    const { washers, dryers } = this.createMachinesList(machines)

    return (
      <>
        <ScreenSaverWrapper
          onUnload={this.updateMachineStatus}
          timeout={LAUNDROMAT_MAP_SCREEN_SAVER_TIMEOUT}
        />
        <MessageAndLoading
          error={errors && showMessage}
          message={errors}
          onOk={() => {
            this.setState({ showMessage: false })
          }}
        >
          {showModal && (
            <ConfirmationModal
              closeText="Cancelar"
              message={
                'Desea activar este equipo?' +
                'Por favor verifique que el mismo se encuentre disponible antes de confirmar'
              }
              okText="Confirmar"
              onClose={() => this.setState({ showModal: false })}
              onOk={() => this.setState({ redirect: true, showModal: false })}
              title="Confirmación"
            />
          )}

          {!isFetching && !isBuildingAvailable && (
            <Row justify="center" type="flex">
              <Col span={20}>
                <ErrorMessage hideBackButton message="BUILDING_NOT_AVAILABLE" />
              </Col>
            </Row>
          )}
          {!isFetching &&
            isBuildingAvailable &&
            isEmpty(washers) &&
            isEmpty(dryers) && (
            <Row justify="center" type="flex">
              <Col span={20}>
                <ErrorMessage
                  hideBackButton
                  message="Network request failed"
                />
              </Col>
            </Row>
          )}
          {isBuildingAvailable && (!isEmpty(washers) || !isEmpty(dryers)) && (
            <Row className="laundromat" justify="center" type="flex">
              <Col className="washer" span={10}>
                {!isEmpty(washers) && (
                  <MachineList
                    data={washers}
                    onActivate={this.onActivateMachine}
                    onCompleteUsage={this.refreshMachineStatusAfterDone}
                    onSelect={this.onSelectMachine}
                    permissions={permissions}
                  />
                )}
              </Col>
              <Col offset={1} span={10}>
                {!isEmpty(dryers) && (
                  <MachineList
                    data={dryers}
                    onActivate={this.onActivateMachine}
                    onCompleteUsage={this.refreshMachineStatusAfterDone}
                    onSelect={this.onSelectMachine}
                    permissions={permissions}
                  />
                )}
              </Col>
            </Row>
          )}
          {redirect && (
            <Redirect
              push
              to={{
                pathname: '/payment',
                state: { selectedMachine },
              }}
            />
          )}
        </MessageAndLoading>
      </>
    )
  }
}

function mapStateToProps(state) {
  return {
    ...state.machineReducer,
    totem: state.totemReducer,
  }
}

function mapDispatchToProps(dispatch) {
  return {
    getMachinesAvailability: (token, buildingId) =>
      dispatch(getMachinesAvailabilityAction(token, buildingId)),
    getBuilding: token => dispatch(getBuildingInfo(token)),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(LaundromatMap)
