import React, { Component } from 'react'
import { connect } from 'react-redux'
import { Redirect } from 'react-router-dom'
import { Row, Col, Button, Icon } from 'antd'
import { Document, Page, pdfjs } from 'react-pdf'

import { sendReceipt as sendReceiptAction } from 'redux/actions/payment'
import { activateMachine as activateMachineAction } from 'redux/actions/machine'

import withMessage from 'components/hocs/MessageHOC'
import withLoading from 'components/hocs/LoadingHOC'

import ScreenSaverWrapper from 'components/ScreenSaver'

import {
  OTHERS_SCREEN_SAVER_TIMEOUT,
  VIEW_RECEIPT_TIMEOUT,
} from '../constants'

const Loading = withLoading('div')
const MessageAndLoading = withMessage(Loading)
// eslint-disable-next-line max-len
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.js`

/*
 * path: /payment/receipt
 */
class ViewReceipt extends Component {
  constructor(props) {
    super(props)
    this.state = { timeoutId: 0 }
  }

  componentDidMount = () => {
    const { transactionEnded, machineActivated, isFetching } = this.props

    if (transactionEnded && !machineActivated && !isFetching) {
      this.activateMachine()
    }
  };

  componentDidUpdate = () => {
    if (!this.props.location.state.email || this.props.receiptSent) {
      if (!this.props.isFetching && !this.state.timeoutId) {
        this.setState({
          timeoutId: setTimeout(this.goToHome, VIEW_RECEIPT_TIMEOUT),
        })
      }
    } else {
      this.sendReceiptToCustomer()
    }
  };

  componentWillUnmount = () => {
    clearTimeout(this.state.timeoutId)
  };

  activateMachine = () => {
    const {
      totem: { token, buildingId },
      activateMachine,
      selectedMachine: { machine_serial },
      transaction: { id },
      location: {
        state: { email },
      },
    } = this.props

    activateMachine(buildingId, machine_serial, email, id, token)
  };

  goToHome = () => {
    this.setState({ backToLaundromat: true })
  };

  sendReceiptToCustomer = () => {
    const {
      isFetching,
      machineActivated,
      sendReceipt,
      location: {
        state: { email },
      },
    } = this.props
    if (isFetching || !machineActivated || !email) return

    const {
      totem: { token },
      transaction: { id },
    } = this.props

    sendReceipt(id, email, token)
  };

  render() {
    const { backToLaundromat } = this.state
    const {
      transaction,
      isFetching,
      errors,
      machineActivated,
      selectedMachine,
    } = this.props
    return (
      <>
        <ScreenSaverWrapper
          onUnload={this.goToHome}
          timeout={OTHERS_SCREEN_SAVER_TIMEOUT}
        />
        <MessageAndLoading
          error={errors}
          isFetching={isFetching && !machineActivated}
          message={errors}
        >
          <Row justify="center" type="flex">
            <Col span={20}>
              <Icon className="success" type="check-circle" />
              <h1>El pago se ha realizado correctamente.</h1>
            </Col>
          </Row>
          {machineActivated && (
            <Row className="laundromat" justify="center" type="flex">
              <Col span={20}>
                <div className="list">
                  <div className="machine info">
                    <div>
                      <h2>
                        Se ha completado el proceso. Estamos activando el equipo{' '}
                      </h2>
                    </div>
                    <div className="number">
                      <div className="index">
                        {selectedMachine.machine_sort_index}
                      </div>
                    </div>
                  </div>
                </div>
              </Col>
            </Row>
          )}
          <Row align="middle" className="receipt" justify="center" type="flex">
            <Col span={10}>
              <h1>Su factura:</h1>
              <div className="information-container">
                {transaction && transaction.receipt && (
                  <Document file={transaction.receipt}>
                    <Page pageNumber={1} scale={0.97} />
                  </Document>
                )}
              </div>
            </Col>
            <Col span={10}>
              <Row justify="center" type="flex">
                <Col span={20}>
                  <Button onClick={this.goToHome} type="primary">
                    Menú Principal
                  </Button>
                </Col>
              </Row>
              {backToLaundromat && (
                <Redirect
                  push
                  to={{
                    pathname: '/',
                  }}
                />
              )}
            </Col>
          </Row>
        </MessageAndLoading>
      </>
    )
  }
}

function mapStateToProps(state) {
  return {
    ...state.paymentReducer,
    machineActivated: state.machineReducer.machineActivated,
    totem: state.totemReducer,
  }
}

function mapDispatchToProps(dispatch) {
  return {
    sendReceipt: (transactionId, email, token) =>
      dispatch(sendReceiptAction(transactionId, email, token)),
    activateMachine: (building, machine, email, transactionId, token) =>
      dispatch(
        activateMachineAction(building, machine, email, transactionId, token),
      ),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(ViewReceipt)
