import React from 'react'
import { Layout, Row, Col, Button } from 'antd'
import { ErrorBoundary } from 'react-error-boundary'

import '../assets/styles/screen.scss'
import 'antd/dist/antd.css'
import FullScreen from 'components/FullScreen'
import ErrorFallback from 'components/ErrorFallback'

import LavomatLogo from '../assets/images/logo-blanco.png'

const MainPage = props => {
  const { Header, Content } = Layout
  const { history, enableBack, mainContent } = props
  return (
    <Layout className="main">
      <Header className="mainHeader">
        <Row justify="center" type="flex">
          {enableBack && (
            <Col span={3}>
              <Button onClick={() => history.goBack()}>ATRÁS</Button>
            </Col>
          )}
          <Col offset={!enableBack ? 3 : 0} span={18}>
            <img
              alt="Lavomat - Solucionamos el lavado de ropa"
              className="logo"
              src={LavomatLogo}
            />
          </Col>
          <Col span={3}>
            <FullScreen />
          </Col>
        </Row>
      </Header>
      <ErrorBoundary fallback={<ErrorFallback />}>
        <Content className="contentMain">{mainContent}</Content>
      </ErrorBoundary>
    </Layout>
  )
}

export default MainPage
