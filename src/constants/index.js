/* eslint-disable max-len */
import packageJson from '../../package.json'

export const BASE_URL = process.env.REACT_APP_API_URL

export const BASE_URL_LOCAL = ''
export const MAX_TRIES_POS_CHECK = 59

export const COMPANY_EMAIL_DOMAIN = '@lavomat.com.uy'

export const REDUX_PERSIST_STORE = 'LAVOMAT_REDUX_PERSIST_STORE'

export const LOG_OUT_PASSWORD = process.env.REACT_APP_LOG_OUT_PASSWORD
export const EXIT_FULL_SCREEN_PASWORD =
  process.env.REACT_APP_EXIT_FULL_SCREEN_PASWORD

export const APP_VERSION = packageJson.version

// 2 minutes
export const SCREEN_REFRESH_TIME =
  process.env.REACT_APP_SCREEN_REFRESH_TIME || 120000

// 15 seconds
export const VIEW_RECEIPT_TIMEOUT =
  process.env.REACT_APP_VIEW_RECEIPT_TIMEOUT || 15000

export const SENTRY_ENVIRONMENT = process.env.REACT_APP_SENTRY_ENVIRONMENT
export const SENTRY_CONFIG = {
  // https://docs.sentry.io/platforms/javascript/guides/react/configuration/options/

  //   Common Options
  dsn: process.env.REACT_APP_SENTRY_DSN,
  environment: process.env.REACT_APP_SENTRY_ENVIRONMENT,
  release: `totem-app@${APP_VERSION}`,
  debug: JSON.parse(process.env.REACT_APP_DEBUG_MODE),
  // % of errors send
  sampleRate: JSON.parse(process.env.REACT_APP_SAMPLE_RATE),

  //   Tracing Options
  // % of 'transactions' send
  tracesSampleRate: JSON.parse(process.env.REACT_APP_TRACES_SAMPLE_RATE),
}

export const ROLES = {
  LAUNDROMAT: 'LAUNDROMAT',
  BUILDING: 'BUILDING',
}
export const PERMISSIONS = {
  ACTIVATION: 'ACTIVATION',
  SHOW_LAST_USER_INFO: 'SHOW_LAST_USER_INFO',
}

// screen saver
export const SCREEN_SAVER_ENABLED =
  process.env.REACT_APP_SCREEN_SAVER_ENABLED === 'true'
export const LAUNDROMAT_MAP_SCREEN_SAVER_TIMEOUT = Number(
  process.env.REACT_APP_LAUNDROMAT_MAP_SCREEN_SAVER_TIMEOUT,
)
export const OTHERS_SCREEN_SAVER_TIMEOUT = Number(
  process.env.REACT_APP_OTHERS_SCREEN_SAVER_TIMEOUT,
)

// these buildings only show machine map
export const MAP_MODE_BUILDINGS = [8]

// alert closure soon - 90 minutes
export const ALERT_CLOSURE_SOON_TIME = 1000 * 60 * 60 * 1.5
