import { createStore, applyMiddleware, compose } from 'redux'
import { createPromise } from 'redux-promise-middleware'

import * as Sentry from '@sentry/react'

import rootReducer from './reducers/rootReducer'

const promise = createPromise()

const middleware = [promise]

const sentryReduxEnhancer = Sentry.createReduxEnhancer()

export default class LavomatApplicationState {
  constructor() {
    this.store = createStore(
      rootReducer,
      compose(applyMiddleware(...middleware), sentryReduxEnhancer),
    )
  }
}
