/* eslint-disable no-case-declarations */

import { trackAction } from 'utils/sentry'
import { MAX_TRIES_POS_CHECK } from '../../constants'

export default function paymentReducer(state = {}, action) {
  trackAction(action)

  switch (action.type) {
  case 'POST_LOGIN_PENDING':
    return { ...state, isFetching: true, token: null, errors: null }
  case 'POST_LOGIN_FULFILLED':
    return {
      ...state,
      isFetching: false,
      token: action.payload.data.token,
      errors: null,
    }
  case 'POST_LOGIN_REJECTED':
    return {
      ...state,
      isFetching: false,
      token: null,
      errors: action.payload.data,
    }
  case 'SAVE_MACHINE':
    return {
      ...state,
      paymentCanceled: false,
      paymentClosed: false,
      cashCollected: false,
      transactionEnded: false,
      selectedMachine: action.selectedMachine,
      errors: null,
    }
  case 'INIT_TRANSACTION_PENDING':
    return {
      ...state,
      isFetching: true,
      transaction: null,
      tries: 0,
      errors: null,
    }
  case 'INIT_TRANSACTION_FULFILLED':
    const {
      Transaction_token,
      Transaction_id,
      Transaction_currency,
      Transaction_amount,
    } = action.payload.data
    return {
      ...state,
      isFetching: false,
      transaction: {
        token: Transaction_token,
        id: Transaction_id,
        currency: Transaction_currency,
        amount: Transaction_amount,
      },
      errors: null,
    }
  case 'INIT_TRANSACTION_REJECTED':
    return {
      ...state,
      isFetching: false,
      errors: action.payload.response,
    }
  case 'COMPLETE_TRANSACTION_PENDING':
    return {
      ...state,
      transactionEnded: false,
      ending: true,
      errors: null,
    }
  case 'COMPLETE_TRANSACTION_FULFILLED':
    return {
      ...state,
      ending: false,
      transactionEnded: true,
      transaction: {
        ...state.transaction,
        receipt: { data: action.payload.data },
      },
      errors: null,
    }
  case 'COMPLETE_TRANSACTION_REJECTED':
    return {
      ...state,
      ending: false,
      errors: action.payload.response,
    }
  case 'CANCEL_POS_TRANSACTION_PENDING':
    return {
      ...state,
      isFetching: true,
      timeout: state.tries > MAX_TRIES_POS_CHECK,
      tries: 0,
      paymentCanceled: false,
      checkingOperation: false,
      errors: null,
    }
  case 'CANCEL_POS_TRANSACTION_FULFILLED':
    return {
      ...state,
      isFetching: false,
      paymentCanceled: true,
      errors: false,
    }
  case 'CANCEL_POS_TRANSACTION_REJECTED':
    return {
      ...state,
      isFetching: false,
      errors: action.payload.response,
    }
  case 'CHECK_STATUS_POS_TRANSACTION_PENDING':
    return {
      ...state,
      checkingOperation: true,
      errors: null,
    }
  case 'CHECK_STATUS_POS_TRANSACTION_FULFILLED':
    const {
      transaction_finished,
      approved,
      state_progress,
    } = action.payload.data
    return {
      ...state,
      cashCollected: transaction_finished,
      checkingOperation: false,
      tries: state.tries ? state.tries + 1 : 1,
      errors: transaction_finished && !approved ? state_progress : false,
    }
  case 'CHECK_STATUS_POS_TRANSACTION_REJECTED':
    return {
      ...state,
      checkingOperation: false,
      errors: action.payload.data,
    }
  case 'SEND_RECEIPT_PENDING':
    return {
      ...state,
      isFetching: true,
      receiptSent: false,
      errors: false,
    }
  case 'SEND_RECEIPT_FULFILLED':
    return {
      ...state,
      isFetching: false,
      receiptSent: action.payload.data['Result:'] === 'OK',
      errors: false,
    }
  case 'SEND_RECEIPT_REJECTED':
    return { ...state, isFetching: false, errors: action.payload.data }
  default:
    return state
  }
}
