import { trackAction } from 'utils/sentry'
import { REDUX_PERSIST_STORE } from '../../constants'

const getStore = () =>
  JSON.parse(window.localStorage.getItem(REDUX_PERSIST_STORE)) || {}
const updateStore = reducer => (state, action) => {
  const store = reducer(state, action)
  window.localStorage.setItem(REDUX_PERSIST_STORE, JSON.stringify(store))
  return store
}

const totemReducer = (state = getStore(), action) => {
  trackAction(action)

  switch (action.type) {
  case 'POST_LOGIN_PENDING':
    return {
      ...state,
      isFetching: true,
      token: null,
      totem: null,
      buildingId: 0,
      errors: null,
    }
  case 'POST_LOGIN_FULFILLED':
    return {
      ...state,
      isFetching: false,
      token: action.payload.data.token,
      totem: action.payload.data.account.user,
      buildingId: action.payload.data.account.user.buildingId,
      role: action.payload.data.account.user.buildingType,
      showLastUserInfo: action.payload.data.account.user.showLastUserInfo,
      errors: null,
    }
  case 'POST_LOGIN_REJECTED':
    return {
      ...state,
      isFetching: false,
      token: null,
      totem: null,
      buildingId: 0,
      errors:
          (action.payload.response &&
            action.payload.response.statusText.toUpperCase()) ||
          'Intente nuevamente!',
    }
  case 'GET_BUILDING_INFO_FULFILLED':
    return {
      ...state,
      buildingId: action.payload.data.id,
      showLastUserInfo: action.payload.data.showLastUserInfo,
      buildingType: action.payload.data.buildingType,
      openingTime: action.payload.data.openingTime,
      closingTime: action.payload.data.closingTime,
    }
  case 'CLEAR_DATA':
    return {}
  default:
    return state
  }
}

export default updateStore(totemReducer)
