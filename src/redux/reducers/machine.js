/* eslint-disable no-case-declarations */

import { filter, sortBy } from 'lodash'

import { trackAction } from 'utils/sentry'

export default function machineReducer(state = {}, action) {
  trackAction(action)

  switch (action.type) {
  case 'GET_MACHINE_AVAILABILITY_PENDING':
    return {
      ...state,
      isFetching: true,
      machineActivated: false,
      machines: null,
      errors: null,
    }
  case 'GET_MACHINE_AVAILABILITY_FULFILLED':
    const { Machines } = action.payload.data
    return {
      ...state,
      isFetching: false,
      machines: sortBy(
        filter(Machines, m => m.machine_sort_index !== 0),
        ['machine_sort_index'],
      ),
      isBuildingAvailable:
          Machines[0] && Machines[0].machine_building_is_enable,
      errors: null,
    }
  case 'GET_MACHINE_AVAILABILITY_REJECTED':
    return {
      ...state,
      isFetching: false,
      machines: [],
      errors: action.payload.data,
    }
  case 'ACTIVATE_MACHINE_PENDING':
    return {
      ...state,
      isFetching: true,
      machines: null,
      errors: null,
    }
  case 'ACTIVATE_MACHINE_FULFILLED':
    return {
      ...state,
      isFetching: false,
      machineActivated: true,
      errors: null,
    }
  case 'ACTIVATE_MACHINE_REJECTED':
    return {
      ...state,
      isFetching: false,
      errors: action.payload.data,
    }
  case 'BOOK_MACHINE_PENDING':
    return {
      ...state,
      isFetching: true,
      bookingId: null,
      bookingSerial: null,
      bookingError: null,
      bookingStatus: null,
      bookingStatusError: null,
      bookingStatusTries: 0,
    }
  case 'BOOK_MACHINE_FULFILLED':
    return {
      ...state,
      isFetching: false,
      bookingId: action.payload.data.id,
      bookingSerial: action.payload.data.serial,
      bookingError: null,
      bookingStatus: null,
      bookingStatusError: null,
    }
  case 'BOOK_MACHINE_REJECTED':
    return {
      ...state,
      isFetching: false,
      bookingId: null,
      bookingSerial: null,
      bookingError: action.payload.response,
    }
  case 'GET_BOOKING_MACHINE_PENDING':
    return {
      ...state,
      isFetching: true,
    }
  case 'GET_BOOKING_MACHINE_FULFILLED':
    return {
      ...state,
      isFetching: false,
      bookingStatus: action.payload.data.status,
      bookingStatusTries: !state.bookingStatusTries
        ? 1
        : state.bookingStatusTries + 1,
    }
  case 'GET_BOOKING_MACHINE_REJECTED':
    return {
      ...state,
      isFetching: false,
      bookingStatus: null,
      bookingStatusError: action.payload.response,
    }
  case 'CANCEL_BOOKING_MACHINE_PENDING':
    return {
      ...state,
      isFetching: true,
      cancelling: true,
    }
  case 'CANCEL_BOOKING_MACHINE_FULFILLED':
    return {
      ...state,
      isFetching: false,
      cancelling: false,
    }

  case 'CANCEL_BOOKING_MACHINE_REJECTED':
    return {
      ...state,
      isFetching: false,
      cancelling: false,
    }
  case 'CLEAR_DATA':
    return {}
  default:
    return state
  }
}
