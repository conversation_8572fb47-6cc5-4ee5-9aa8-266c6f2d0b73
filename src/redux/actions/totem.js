import { get, post } from 'utils/lavomatClient';
import { COMPANY_EMAIL_DOMAIN } from '../../constants';

export function clearData() {
  return {
    type: 'CLEAR_DATA',
  };
}

export function authenticateTotem({ username, password }) {
  return {
    type: 'POST_LOGIN',
    payload: post('/api/v1/signin', {
      emailAddress: `${username}${COMPANY_EMAIL_DOMAIN}`,
      password,
    }),
  };
}

export function getBuildingInfo(token) {
  return {
    type: 'GET_BUILDING_INFO',
    payload: get('/totem/v1/building?level=1', {}, token),
  };
}
