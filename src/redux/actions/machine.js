import { deleteAction, get, post } from 'utils/lavomatClient'

export function getMachinesAvailability(token, building_id) {
  return {
    type: 'GET_MACHINE_AVAILABILITY',
    payload: get(`/api/v1/machine/status/${building_id}`, {}, token),
  }
}

export function activateMachine(
  building_id,
  machine_id,
  email,
  transactionId,
  token,
) {
  return {
    type: 'ACTIVATE_MACHINE',
    payload: get(
      '/api/v1/activate/request',
      {
        param1: 'null',
        param2: building_id,
        param3: machine_id,
        param4: true,
        param5: email ? encodeURIComponent(email) : '',
        param6: '0',
        param7: 'null',
        param8: 'TOTEM',
        param9: transactionId,
      },
      token,
    ),
  }
}

export const bookMachine = ({ serial }, token) => ({
  type: 'BOOK_MACHINE',
  payload: post('/totem/v1/booking', { serial }, token),
})

export const getBookingMachine = ({ id }, token) => ({
  type: 'GET_BOOKING_MACHINE',
  payload: get(`/totem/v1/booking/${id}`, {}, token),
})

export const cancelBookingMachine = ({ id }, token) => ({
  type: 'CANCEL_BOOKING_MACHINE',
  payload: deleteAction(`/totem/v1/booking/${id}`, {}, token),
})
