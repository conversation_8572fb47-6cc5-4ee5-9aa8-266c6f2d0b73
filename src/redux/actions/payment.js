import { post, postForPDF, get } from 'utils/lavomatClient'

export function saveSelectedMachine(selectedMachine) {
  return {
    type: 'SAVE_MACHINE',
    selectedMachine,
  }
}

export function initTransaction(token, rut, selectedMachine) {
  return {
    type: 'INIT_TRANSACTION',
    payload: post(
      '/api/v1/totem/order',
      { serial: selectedMachine.machine_serial, rut },
      token,
    ),
  }
}

export function initTransactionPOS(token, selectedMachine, billingInfo) {
  return {
    type: 'INIT_TRANSACTION',
    payload: post(
      '/api/v1/transaction/transact',
      { serial: selectedMachine.machine_serial, ...billingInfo },
      token,
    ),
  }
}

export function completeTransaction(transactionId, token) {
  return {
    type: 'COMPLETE_TRANSACTION',
    payload: postForPDF(
      `/api/v1/totem/order/${transactionId}/confirm`,
      {},
      token,
    ),
  }
}

export function completeTransactionPOS(transactionId, token) {
  return {
    type: 'COMPLETE_TRANSACTION',
    payload: postForPDF(
      `/api/v1/transaction/transact/${transactionId}/confirm`,
      {},
      token,
    ),
  }
}

export function cancelTransactionPOS(transactionId, transactionToken, token) {
  return {
    type: 'CANCEL_POS_TRANSACTION',
    payload: post(
      `/api/v1/transaction/transact/${transactionId}/reject`,
      { message: '', transaction_token: transactionToken },
      token,
    ),
  }
}

export function checkTransactionPOS(transactionToken, token) {
  return {
    type: 'CHECK_STATUS_POS_TRANSACTION',
    payload: get(
      '/api/v1/transaction/transact/status',
      { transaction_token: transactionToken },
      token,
    ),
  }
}

export function sendReceipt(transactionId, email, token) {
  return {
    type: 'SEND_RECEIPT',
    payload: post(
      '/api/v1/totem/order/send',
      { id: transactionId, email },
      token,
    ),
  }
}
